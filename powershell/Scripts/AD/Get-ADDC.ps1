
<#
.SYNOPSIS
    Gets Active Directory Domain Controllers for a specified domain.

.DESCRIPTION
    Retrieves domain controllers from Active Directory using stored credentials.
    Returns JSON formatted results compatible with the AutoDeploy service.

.PARAMETER domain
    The domain to query for domain controllers.

.PARAMETER appType
    The type of application (affects credential target).

.PARAMETER siteName
    Optional. Filter domain controllers by site name.

.EXAMPLE
    .\Get-ADDC.ps1 -domain "example.com" -appType "VDI"

.EXAMPLE
    .\Get-ADDC.ps1 -domain "example.com" -appType "Shared" -siteName "Default-First-Site-Name"

.NOTES
    File Name   : Get-ADDC.ps1
    Author      : <PERSON><PERSON>
    Version     : 1.0 - Initial script for querying domain controllers
#>
#Requires -Version 5
#Requires -<PERSON><PERSON><PERSON>redential<PERSON>ger, ActiveDirectory

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$domain,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$appType,

    [Parameter(Mandatory = $false)]
    [string]$siteName = "AWS01A01",

    [Parameter(ValueFromRemainingArguments = $true)]
    [string[]]$RemainingArguments
)

function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$success,

        [Parameter(Mandatory = $true)]
        [string]$status,

        [Parameter(Mandatory = $true)]
        [string]$message,

        [Parameter(Mandatory = $false)]
        [array]$domainControllers = @()
    )

    $objectReturn = @{
        domain             = $domain
        siteName           = $siteName
        domainControllers  = $domainControllers
        timeStamp          = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }

    $jsonResponse = @{
        success = $success
        status  = $status
        message = $message
        data    = $objectReturn
    }

    return ($jsonResponse | ConvertTo-Json -Depth 3)
}

function Test-ADCredentials {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]$Credential,

        [Parameter(Mandatory = $true)]
        [string]$Domain
    )

    try {
        $null = Get-ADDomain -Server $Domain -Credential $Credential -ErrorAction Stop
        return $true
    }
    catch {
        Write-Debug "Credential validation failed: $($_.Exception.Message)"
        return $false
    }
}

function Get-ADSiteInfo {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Domain,

        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]$Credential,

        [Parameter(Mandatory = $false)]
        [string]$SiteName = "AWS01A01"
    )

    try {
        $domainInfo = Get-ADDomain -Server $Domain -Credential $Credential -ErrorAction Stop
        $configurationNamingContext = $domainInfo.Forest | Get-ADForest -Server $Domain -Credential $Credential | Select-Object -ExpandProperty PartitionsContainer
        $sitesContainer = "CN=Sites,$configurationNamingContext"

        if ($SiteName) {
            $sites = Get-ADObject -Filter "ObjectClass -eq 'site' -and Name -eq '$SiteName'" -SearchBase $sitesContainer -Server $Domain -Credential $Credential -Properties Name, Description, siteObjectBL
            if (-not $sites) {
                return $null
            }
        }
        else {
            $sites = Get-ADObject -Filter "ObjectClass -eq 'site'" -SearchBase $sitesContainer -Server $Domain -Credential $Credential -Properties Name, Description, siteObjectBL
        }

        $siteList = $sites | ForEach-Object {
            @{
                Name = $_.Name
                DistinguishedName = $_.DistinguishedName
                Description = $_.Description
                SubnetCount = if ($_.siteObjectBL) { $_.siteObjectBL.Count } else { 0 }
            }
        }

        return $siteList
    }
    catch {
        Write-Debug "Failed to retrieve AD sites: $($_.Exception.Message)"
        return $null
    }
}



try {
    $credentialTarget = if ($appType -eq "VDI") { "VDI" } else { $domain }

    try {
        $adCreds = Get-StoredCredential -Target $credentialTarget -ErrorAction Stop
        if (-not $adCreds) {
            throw "No credentials found for target: $credentialTarget"
        }
    }
    catch {
        $errorMessage = "Failed to retrieve stored credentials for target '$credentialTarget': $($_.Exception.Message)"
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }

    if (-not (Test-ADCredentials -Credential $adCreds -Domain $domain)) {
        $errorMessage = "Credential validation failed for domain: $domain. Please check stored credentials."
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }

    try {
        $dcParams = @{
            Server = $domain
            Credential = $adCreds
            ErrorAction = 'Stop'
        }

        if ($siteName) {
            $domainControllers = Get-ADDomainController -Filter * @dcParams | Where-Object { $_.Site -eq $siteName }
            if (-not $domainControllers) {
                $errorMessage = "No domain controllers found in site: $siteName for domain: $domain"
                return (New-JsonReturn -success "false" -status "WARNING" -message $errorMessage)
            }
        }
        else {
            $domainControllers = Get-ADDomainController -Filter * @dcParams
        }

        $dcList = $domainControllers | ForEach-Object {
            @{
                Name = $_.Name
                HostName = $_.HostName
                Site = $_.Site
                IPv4Address = $_.IPv4Address
                OperatingSystem = $_.OperatingSystem
                Enabled = $_.Enabled
            }
        }

        $message = if ($siteName) {
            "Found $($dcList.Count) domain controller(s) in site '$siteName' for domain '$domain'"
        } else {
            "Found $($dcList.Count) domain controller(s) for domain '$domain'"
        }

        return (New-JsonReturn -success "true" -status "COMPLETED" -message $message -domainControllers $dcList)
    }
    catch {
        $errorMessage = "Failed to retrieve domain controllers: $($_.Exception.Message)"
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }
}
catch {
    $errorMessage = "Unexpected error: $($_.Exception.Message)"
    return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
}

