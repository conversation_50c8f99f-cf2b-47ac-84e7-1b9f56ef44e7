<#
.SYNOPSIS
    Gets Active Directory Sites for a specified domain.

.DESCRIPTION
    Retrieves AD sites from Active Directory using stored credentials.
    Returns JSON formatted results compatible with the AutoDeploy service.

.PARAMETER domain
    The domain to query for AD sites.

.PARAMETER appType
    The type of application (affects credential target).

.PARAMETER siteName
    Optional. Get specific site information.

.EXAMPLE
    .\Get-ADSite.ps1 -domain "example.com" -appType "VDI"

.EXAMPLE
    .\Get-ADSite.ps1 -domain "example.com" -appType "Shared" -siteName "Default-First-Site-Name"

.NOTES
    File Name   : Get-ADSite.ps1
    Author      : <PERSON><PERSON>
    Version     : 1.0 - Initial script for querying AD sites
#>
#Requires -Version 5
#Requires -<PERSON><PERSON>les CredentialManager, ActiveDirectory

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$domain,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$appType,

    [Parameter(Mandatory = $false)]
    [string]$siteName = "AWS01A01",

    [Parameter(ValueFromRemainingArguments = $true)]
    [string[]]$RemainingArguments
)

function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$success,

        [Parameter(Mandatory = $true)]
        [string]$status,

        [Parameter(Mandatory = $true)]
        [string]$message,

        [Parameter(Mandatory = $false)]
        [array]$sites = @()
    )

    $objectReturn = @{
        domain    = $domain
        siteName  = $siteName
        sites     = $sites
        timeStamp = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }

    $jsonResponse = @{
        success = $success
        status  = $status
        message = $message
        data    = $objectReturn
    }

    return ($jsonResponse | ConvertTo-Json -Depth 3)
}

function Test-ADCredentials {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]$Credential,

        [Parameter(Mandatory = $true)]
        [string]$Domain
    )

    try {
        $null = Get-ADDomain -Server $Domain -Credential $Credential -ErrorAction Stop
        return $true
    }
    catch {
        Write-Debug "Credential validation failed: $($_.Exception.Message)"
        return $false
    }
}

try {
    $credentialTarget = if ($appType -eq "VDI") { "VDI" } else { $domain }

    try {
        $adCreds = Get-StoredCredential -Target $credentialTarget -ErrorAction Stop
        if (-not $adCreds) {
            throw "No credentials found for target: $credentialTarget"
        }
    }
    catch {
        $errorMessage = "Failed to retrieve stored credentials for target '$credentialTarget': $($_.Exception.Message)"
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }

    if (-not (Test-ADCredentials -Credential $adCreds -Domain $domain)) {
        $errorMessage = "Credential validation failed for domain: $domain. Please check stored credentials."
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }

    try {
        $domainInfo = Get-ADDomain -Server $domain -Credential $adCreds -ErrorAction Stop
        $configurationNamingContext = $domainInfo.Forest | Get-ADForest -Server $domain -Credential $adCreds | Select-Object -ExpandProperty PartitionsContainer
        $sitesContainer = "CN=Sites,$configurationNamingContext"

        if ($siteName) {
            $sites = Get-ADObject -Filter "ObjectClass -eq 'site' -and Name -eq '$siteName'" -SearchBase $sitesContainer -Server $domain -Credential $adCreds -Properties Name, Description, siteObjectBL
            if (-not $sites) {
                $errorMessage = "Site '$siteName' not found in domain: $domain"
                return (New-JsonReturn -success "false" -status "WARNING" -message $errorMessage)
            }
        }
        else {
            $sites = Get-ADObject -Filter "ObjectClass -eq 'site'" -SearchBase $sitesContainer -Server $domain -Credential $adCreds -Properties Name, Description, siteObjectBL
        }

        $siteList = $sites | ForEach-Object {
            @{
                Name = $_.Name
                DistinguishedName = $_.DistinguishedName
                Description = $_.Description
                SubnetCount = if ($_.siteObjectBL) { $_.siteObjectBL.Count } else { 0 }
            }
        }

        $message = if ($siteName) {
            "Found site '$siteName' in domain '$domain'"
        } else {
            "Found $($siteList.Count) site(s) in domain '$domain'"
        }

        return (New-JsonReturn -success "true" -status "COMPLETED" -message $message -sites $siteList)
    }
    catch {
        $errorMessage = "Failed to retrieve AD sites: $($_.Exception.Message)"
        return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
    }
}
catch {
    $errorMessage = "Unexpected error: $($_.Exception.Message)"
    return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
}
